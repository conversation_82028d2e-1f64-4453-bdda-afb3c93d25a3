<?xml version="1.0" encoding="utf-8"?><!-- 根布局使用ConstraintLayout，便��将滚动视图和固定的底部栏分离 -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"> <!-- 设置一个浅灰色背景 -->

    <!-- ScrollView用于承载可滚动的内容区域 -->
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@+id/bottom_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 垂直LinearLayout作为滚动内容的主容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 1. 顶部Banner区域 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="160dp"
                android:background="@drawable/bg_top_banner"> <!-- 假设这是一个深色带波浪纹的drawable -->

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="7dp"
                    android:layout_height="14dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginTop="14dp"
                    android:contentDescription="返回"
                    android:src="@drawable/arrow_back"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- Banner内的文本信息 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/guideline_vertical_50"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="超级折扣会员卡"
                        android:textColor="@android:color/white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="AI功能畅享用"
                        android:textColor="@android:color/white"
                        android:textSize="12sp" />
                </LinearLayout>

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_vertical_50"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/guideline_vertical_50"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="折扣会员卡"
                        android:textColor="#FFFFFF"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="课程折扣8.8折"
                        android:textColor="#FFFFFF"
                        android:textSize="12sp" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 2. 购买选项卡片区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="41dp"
                android:orientation="horizontal"
                android:paddingHorizontal="20dp">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:baselineAligned="false"
                    android:gravity="bottom"
                    android:orientation="horizontal">

                    <!-- 单独购买卡片 (已重构为ConstraintLayout以实现标签重叠效果) -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        app:strokeColor="#E0E0E0"
                        app:strokeWidth="1dp">

                        <!--
                          使用ConstraintLayout作为唯一子项，用于灵活定位内部元素。
                        -->
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <!--
                              主要内容区域，使用LinearLayout。
                              - layout_marginTop将整个白色区域向下推，为顶部的红色标签留出空间。
                              - paddingTop为内部的 "单独购买" 等文本提供了与顶部边缘的间距。
                            -->
                            <LinearLayout
                                android:id="@+id/content_area"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:background="@drawable/bg_promo_tag_solidyellow"
                                android:gravity="center_horizontal"
                                android:orientation="vertical"
                                android:paddingTop="20dp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="单独购买"
                                    android:textColor="@android:color/black"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="8dp"
                                    android:gravity="bottom"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="¥"
                                        android:textColor="@android:color/black"
                                        android:textSize="18sp" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="17.9"
                                        android:textColor="@android:color/black"

                                        android:textSize="32sp"
                                        android:textStyle="bold" />
                                </LinearLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:text="原价¥19.9"
                                    android:textColor="#9E9E9E"
                                    android:textSize="12sp"
                                    tools:paint_flags="strike_thru_text" /> <!-- 删除线效果 -->

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="12dp"
                                    android:background="@drawable/bg_promo_tag_danyellow"
                                    android:gravity="center"
                                    android:paddingVertical="8dp"
                                    android:text="立省¥2"
                                    android:textColor="#FE5656" />

                            </LinearLayout>


                            <LinearLayout
                                android:id="@+id/promo_tag_container"
                                android:layout_width="match_parent"
                                android:layout_height="22dp"
                                android:layout_marginTop="-11dp"
                                android:layout_marginEnd="5dp"
                                android:background="@drawable/bg_promo_tag_red"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:paddingHorizontal="18dp"
                                app:layout_constraintStart_toStartOf="@+id/content_area"
                                app:layout_constraintTop_toTopOf="@+id/content_area">


                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="限时优惠2元"
                                    android:textColor="@android:color/white"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />


                                <TextView
                                    android:layout_width="wrap_content"

                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="2dp"
                                    android:singleLine="true"
                                    android:text="23:59:59"
                                    android:textColor="@android:color/white"
                                    android:textSize="9sp" />

                                <TextView
                                    android:layout_width="wrap_content"

                                    android:layout_height="wrap_content"
                                    android:singleLine="true"
                                    android:text="后失效"
                                    android:textColor="@android:color/white"
                                    android:textSize="9sp" />

                            </LinearLayout>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- 卡片间的水平间距 -->
                    <View
                        android:layout_width="15dp"
                        android:layout_height="0dp" />

                    <!-- 拼团购买卡片 (高亮) (已根据您的代码更新) -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#FFF8E1"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        app:strokeColor="#FFB74D"
                        app:strokeWidth="1dp"> <!-- 高亮背景色 -->

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_promo_tag_gradingyellow"
                            android:gravity="center_horizontal"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp"
                                android:text="拼团购买"
                                android:textColor="#E65100"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:gravity="bottom"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="¥"
                                    android:textColor="#F44336"
                                    android:textSize="18sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="14.9"
                                    android:textColor="#F44336"
                                    android:textSize="32sp"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="原价¥19.9"
                                android:textColor="#9E9E9E"
                                android:textSize="12sp"
                                tools:paint_flags="strike_thru_text" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp"
                                android:background="@drawable/bg_promo_tag_danyellow"
                                android:gravity="center"
                                android:paddingVertical="8dp"
                                android:text="立省¥5"
                                android:textColor="#E65100" />
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>
            </LinearLayout>

            <!-- 3. 会员权益表格 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="24dp"
                android:text="会员卡享受8大权益"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold" />


            <!--
  使用 ConstraintLayout 作为根容器，这是实现复杂重叠布局的最佳选择。
  - 关键: 禁用裁剪，为内部的“溢出”做准备。
-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="15dp"
                android:layout_marginTop="26dp"
                android:layout_marginBottom="16dp"
                android:clipToPadding="false"
                android:clipChildren="false">

                <!--
                  1. 背景板: 一个只负责显示背景和阴影的 CardView。
                  - 它不包含任何子视图。
                  - 注意：它的高度被设置为与下方的内容LinearLayout一致。
                -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/table_background_card"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:cardBackgroundColor="@android:color/white"
                    app:layout_constraintTop_toTopOf="@+id/table_content_layout"
                    app:layout_constraintBottom_toBottomOf="@+id/table_content_layout"
                    app:layout_constraintStart_toStartOf="@+id/table_content_layout"
                    app:layout_constraintEnd_toEndOf="@+id/table_content_layout"/>

                <!--
                  2. 内容层: 一个包含所有列的 LinearLayout。
                  - 它位于背景板 CardView 的“上方”。
                  - 它不再被任何CardView裁剪！
                -->
                <LinearLayout
                    android:id="@+id/table_content_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:baselineAligned="false"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <!-- 列1: 特权 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.1"
                        android:orientation="vertical">
                        <include layout="@layout/table_column_privileges" />
                    </LinearLayout>

                    <!--
                      列2: 超级会员卡 (高亮)
                      - 现在，负边距可以毫无阻碍地生效了！
                    -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:background="@drawable/bg_table_highlight_column"
                        android:layout_marginTop="-10dp"
                        android:layout_marginBottom="-10dp"
                        android:paddingBottom="10dp"> <!-- 提高它的 elevation，让它浮在最上层 -->
                        <include layout="@layout/table_column_super_vip" />
                    </LinearLayout>

                    <!-- 列3: 会员卡 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">
                        <include layout="@layout/table_column_vip" />
                    </LinearLayout>

                    <!-- 列4: 普通用户 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">
                        <include layout="@layout/table_column_regular" />
                    </LinearLayout>
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </ScrollView>

    <!-- 4. 底部固定操作栏 -->
    <LinearLayout
        android:id="@+id/bottom_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_join_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient_button_red"
            app:backgroundTint="@null"
            android:paddingVertical="12dp"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            app:cornerRadius="28dp"
            android:textAllCaps="false">

            <!--
              使用一个垂直的LinearLayout来容纳两行文本。
              这比使用HTML标签提供了更好的控制和性能。
            -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal">

                <!-- 主标题 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥12.9 立即拼团"
                    android:textColor="@android:color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"/>

                <!-- 副标题 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="邀请1名新用户"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    android:layout_marginTop="2dp"
                    android:alpha="0.8"/> <!-- 使用透明度让副标题稍微弱化 -->
            </LinearLayout>

        </com.google.android.material.button.MaterialButton>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/cb_agree"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:minWidth="0dp"
                android:minHeight="0dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="我已阅读并同意会员协议，且知晓权益为一年有效期"
                android:textColor="#616161"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>